#!/usr/bin/env python
"""
简单的异常检测集成测试
"""

import os
import sys
from pathlib import Path

# 设置环境变量启用真实仿真系统
os.environ['USE_REAL_SIMULATION'] = 'true'

# 添加路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / 'pv_digital_twin'))
sys.path.insert(0, str(project_root / 'src'))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pv_digital_twin.settings')

try:
    import django
    django.setup()
    print("✓ Django环境设置成功")
except Exception as e:
    print(f"✗ Django环境设置失败: {e}")
    sys.exit(1)

def test_basic_imports():
    """测试基础模块导入"""
    print("\n1. 测试基础模块导入:")
    
    try:
        from dashboard.config import SimulationConfig
        print("  ✓ SimulationConfig导入成功")
    except Exception as e:
        print(f"  ✗ SimulationConfig导入失败: {e}")
        return False
    
    try:
        from dashboard.pv_model_adapter import PVModelAdapter
        print("  ✓ PVModelAdapter导入成功")
    except Exception as e:
        print(f"  ✗ PVModelAdapter导入失败: {e}")
        return False
    
    return True

def test_anomaly_model_import():
    """测试异常检测模型导入"""
    print("\n2. 测试异常检测模型导入:")
    
    try:
        # 直接导入异常检测模型
        sys.path.insert(0, str(project_root / 'src' / 'model'))
        from anomaly_model import AnomalyModel
        print("  ✓ AnomalyModel导入成功")
        return True
    except Exception as e:
        print(f"  ✗ AnomalyModel导入失败: {e}")
        return False

def test_lstm_kan_import():
    """测试LSTM+KAN模型导入"""
    print("\n3. 测试LSTM+KAN模型导入:")
    
    try:
        sys.path.insert(0, str(project_root / 'src' / 'model'))
        from lstm_kan_architecture import LSTMKATAutoencoder
        print("  ✓ LSTMKATAutoencoder导入成功")
        return True
    except Exception as e:
        print(f"  ✗ LSTMKATAutoencoder导入失败: {e}")
        return False

def test_adapter_initialization():
    """测试适配器初始化"""
    print("\n4. 测试适配器初始化:")
    
    try:
        from dashboard.config import SimulationConfig
        from dashboard.pv_model_adapter import PVModelAdapter
        
        print(f"  - 使用真实仿真系统: {SimulationConfig.use_real_simulation()}")
        
        # 获取适配器实例
        adapter = PVModelAdapter.get_instance()
        print(f"  ✓ 适配器类型: {type(adapter).__name__}")
        
        # 检查异常检测模型
        has_anomaly_model = hasattr(adapter, 'anomaly_model') and adapter.anomaly_model is not None
        print(f"  - 异常检测模型: {'已加载' if has_anomaly_model else '未加载'}")
        
        if has_anomaly_model:
            has_lstm_kan = hasattr(adapter.anomaly_model, 'detection_model') and adapter.anomaly_model.detection_model is not None
            print(f"  - LSTM+KAN模型: {'已加载' if has_lstm_kan else '未加载'}")
            
            if hasattr(adapter.anomaly_model, 'model_path'):
                print(f"  - 模型文件路径: {adapter.anomaly_model.model_path}")
        
        return True
    except Exception as e:
        print(f"  ✗ 适配器初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_anomaly_api():
    """测试异常检测API"""
    print("\n5. 测试异常检测API:")
    
    try:
        from dashboard.pv_model_adapter import PVModelAdapter
        
        adapter = PVModelAdapter.get_instance()
        
        # 测试异常检测API
        anomalies = adapter.get_detected_anomalies()
        print(f"  ✓ API调用成功")
        print(f"  - 检测到的异常数量: {len(anomalies)}")
        
        if anomalies:
            print(f"  - 异常示例:")
            for i, anomaly in enumerate(anomalies[:2]):
                print(f"    [{i+1}] {anomaly.get('type', 'N/A')} (严重程度: {anomaly.get('severity', 0.0):.3f})")
        
        return True
    except Exception as e:
        print(f"  ✗ 异常检测API测试失败: {e}")
        return False

def test_detect_directory():
    """测试detect目录保护"""
    print("\n6. 测试detect目录保护:")
    
    detect_path = project_root / 'detect'
    if not detect_path.exists():
        print(f"  ✗ detect目录不存在")
        return False
    
    critical_files = [
        'lstm_kat_autoencoder.pth',
        'lstm+katransformer.py', 
        'lstm+transformer_pred.py',
        '模型说明.md'
    ]
    
    all_exist = True
    for file_name in critical_files:
        file_path = detect_path / file_name
        if file_path.exists():
            print(f"  ✓ {file_name}: 存在 ({file_path.stat().st_size} bytes)")
        else:
            print(f"  ✗ {file_name}: 缺失")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("=" * 60)
    print("阶段三：异常检测系统集成 - 简单测试")
    print("=" * 60)
    
    tests = [
        test_basic_imports,
        test_lstm_kan_import,
        test_anomaly_model_import,
        test_adapter_initialization,
        test_anomaly_api,
        test_detect_directory
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"  ✗ 测试异常: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 阶段三重构成功完成！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
