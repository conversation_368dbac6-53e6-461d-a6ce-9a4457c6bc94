#!/usr/bin/env python
"""
测试异常检测系统集成脚本

此脚本用于测试阶段三的异常检测系统集成，包括：
1. 启用真实仿真系统
2. 测试LSTM+KAN异常检测模型加载
3. 验证异常检测API兼容性
4. 测试异常效应应用
"""

import os
import sys
import django
from pathlib import Path

# 设置项目路径
project_root = Path(__file__).parent
pv_digital_twin_path = project_root / 'pv_digital_twin'
sys.path.insert(0, str(pv_digital_twin_path))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pv_digital_twin.settings')
os.environ['USE_REAL_SIMULATION'] = 'true'  # 启用真实仿真系统

django.setup()

def test_anomaly_detection_integration():
    """测试异常检测系统集成"""
    print("=" * 60)
    print("阶段三：异常检测系统集成测试")
    print("=" * 60)
    
    # 导入必要的模块
    from dashboard.config import SimulationConfig
    from dashboard.pv_model_adapter import PVModelAdapter
    
    print(f"1. 配置检查:")
    print(f"   - 使用真实仿真系统: {SimulationConfig.use_real_simulation()}")
    
    # 验证src模块导入
    success, message = SimulationConfig.validate_src_modules()
    print(f"   - src模块验证: {'成功' if success else '失败'}")
    if not success:
        print(f"     错误信息: {message}")
        return False
    
    print(f"\n2. 适配器初始化:")
    try:
        # 获取适配器实例
        adapter = PVModelAdapter.get_instance()
        print(f"   - 适配器类型: {type(adapter).__name__}")
        
        # 检查是否有异常检测模型
        has_anomaly_model = hasattr(adapter, 'anomaly_model') and adapter.anomaly_model is not None
        print(f"   - 异常检测模型: {'已加载' if has_anomaly_model else '未加载'}")
        
        if has_anomaly_model:
            # 检查LSTM+KAN模型是否加载
            has_lstm_kan = hasattr(adapter.anomaly_model, 'detection_model') and adapter.anomaly_model.detection_model is not None
            print(f"   - LSTM+KAN模型: {'已加载' if has_lstm_kan else '未加载'}")
            print(f"   - 异常检测阈值: {adapter.anomaly_model.threshold}")
            
            # 检查模型路径
            if hasattr(adapter.anomaly_model, 'model_path'):
                print(f"   - 模型文件路径: {adapter.anomaly_model.model_path}")
        
    except Exception as e:
        print(f"   - 错误: {e}")
        return False
    
    print(f"\n3. 异常检测API测试:")
    try:
        # 测试异常检测API
        anomalies = adapter.get_detected_anomalies()
        print(f"   - API调用: 成功")
        print(f"   - 检测到的异常数量: {len(anomalies)}")
        
        # 显示前几个异常（如果有）
        if anomalies:
            print(f"   - 异常示例:")
            for i, anomaly in enumerate(anomalies[:3]):
                print(f"     [{i+1}] 时间: {anomaly.get('timestamp', 'N/A')}")
                print(f"         类型: {anomaly.get('type', 'N/A')}")
                print(f"         严重程度: {anomaly.get('severity', 0.0):.3f}")
                print(f"         描述: {anomaly.get('description', 'N/A')}")
        else:
            print(f"   - 当前没有检测到异常")
        
    except Exception as e:
        print(f"   - API测试失败: {e}")
        return False
    
    print(f"\n4. 仿真数据测试:")
    try:
        # 测试仿真数据获取
        sim_data = adapter.get_simulation_data()
        print(f"   - 仿真数据获取: 成功")
        print(f"   - 数据点数量: {len(sim_data.get('timestamps', []))}")
        
        # 检查数据格式
        required_keys = ['timestamps', 'ac_power', 'dc_power', 'temp_air', 'temp_cell', 'ghi', 'efficiency']
        missing_keys = [key for key in required_keys if key not in sim_data]
        if missing_keys:
            print(f"   - 警告: 缺少数据字段: {missing_keys}")
        else:
            print(f"   - 数据格式: 完整")
        
    except Exception as e:
        print(f"   - 仿真数据测试失败: {e}")
        return False
    
    print(f"\n5. 系统信息测试:")
    try:
        # 测试系统信息获取
        sys_info = adapter.get_system_info()
        print(f"   - 系统信息获取: 成功")
        print(f"   - 安装容量: {sys_info.get('installed_capacity', 'N/A')} kW")
        print(f"   - 当前功率: {sys_info.get('current_power', 'N/A')} kW")
        
    except Exception as e:
        print(f"   - 系统信息测试失败: {e}")
        return False
    
    print(f"\n6. 仿真状态测试:")
    try:
        # 测试仿真状态获取
        status = adapter.get_simulation_status()
        print(f"   - 仿真状态获取: 成功")
        print(f"   - 运行状态: {'运行中' if status.get('is_running', False) else '已停止'}")
        print(f"   - 暂停状态: {'已暂停' if status.get('is_paused', False) else '正常'}")
        print(f"   - 异常检测模型: {'可用' if status.get('has_anomaly_model', False) else '不可用'}")
        
    except Exception as e:
        print(f"   - 仿真状态测试失败: {e}")
        return False
    
    print(f"\n" + "=" * 60)
    print("测试完成：异常检测系统集成成功！")
    print("=" * 60)
    return True

def test_detect_directory_protection():
    """测试detect目录保护"""
    print(f"\n7. detect目录保护测试:")
    
    detect_path = project_root / 'detect'
    if not detect_path.exists():
        print(f"   - 警告: detect目录不存在")
        return False
    
    # 检查关键文件
    critical_files = [
        'lstm_kat_autoencoder.pth',
        'lstm+katransformer.py',
        'lstm+transformer_pred.py',
        '模型说明.md'
    ]
    
    missing_files = []
    for file_name in critical_files:
        file_path = detect_path / file_name
        if file_path.exists():
            print(f"   - ✓ {file_name}: 存在 ({file_path.stat().st_size} bytes)")
        else:
            missing_files.append(file_name)
            print(f"   - ✗ {file_name}: 缺失")
    
    if missing_files:
        print(f"   - 警告: 缺少关键文件: {missing_files}")
        return False
    else:
        print(f"   - detect目录完整性: 良好")
        return True

if __name__ == "__main__":
    try:
        # 运行异常检测集成测试
        success = test_anomaly_detection_integration()
        
        # 运行detect目录保护测试
        detect_success = test_detect_directory_protection()
        
        if success and detect_success:
            print(f"\n🎉 阶段三重构成功完成！")
            print(f"   - LSTM+KAN异常检测模型已集成")
            print(f"   - Django API兼容性已保持")
            print(f"   - detect目录文件已保护")
            sys.exit(0)
        else:
            print(f"\n❌ 测试失败，请检查错误信息")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
